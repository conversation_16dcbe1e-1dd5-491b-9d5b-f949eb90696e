{"name": "hia-suag-ma", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "rm-branchs": "git branch | grep -v \"main\" | xargs git branch -D"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@prisma/client": "^6.9.0", "@radix-ui/react-popover": "^1.1.14", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cookies-next": "^6.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.17.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "socket.io-client": "^4.8.1", "styled-components": "^6.1.18", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/socket.io-client": "^1.4.36", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prisma": "^6.9.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}